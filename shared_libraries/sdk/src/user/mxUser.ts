import { User, SystemPasswordRule } from '@moxo/proto';
import { IRequestPromise } from '../network/ajax';
export enum MxLoginType {
  APPLE_JWT = 'APPLE_JWT',
  GOOGLE_JWT = 'GOOGLE_JWT',
  SALESFORCE_JWT = 'SALESFORCE_JWT',
  VERIFICATION_CODE = 'VERIFICATION_CODE',
  ORG_INVITE_TOKEN = 'ORG_INVITE_TOKEN',
}
export interface LoginOption {
  email?: string;
  phone_number?: string;
  pass?: string;
  rememberMe?: boolean;
  notLoadFullUser?: boolean;
  isMep?: boolean;
  isOrgAdmin?: boolean;
  isSuperAdmin?: boolean;
  isPartnerAdmin?: boolean;

  // 2fa support
  deviceId?: string;
  verificationCode?: string;
  verificationCodeType?: string; // email / sms
  rememberDevice?: boolean;

  // password free
  loginType?: MxLoginType;
  appleJWT?: string;
  googleJWT?: string;
  salesforceJWT?: string;
  orgInviteToken?: string;

  // for pending user, allow to update name, title at login phase
  firstName?: string;
  lastName?: string;
  title?: string;
}

export interface IMxUser {
  readonly id: string;
  readonly _user: User;
  verifyToken(opt?: LoginOption): IRequestPromise<User>;

  login(opt?: LoginOption): Promise<User>;

  loginWithAccessToken(token: string): IRequestPromise<User>;

  logout(logoutAllDevice?: boolean): IRequestPromise<void>;

  register(user: User, token?: string, isPartnerAdmin?: boolean): IRequestPromise<User>;

  resetPassword(email: string, isPartnerAdminExpected?: boolean): IRequestPromise<void>;

  resetPasswordWithPhoneNum(
    phoneNum: string,
    verifyCode: string,
    password: string,
  ): IRequestPromise<void>;

  resetPasswordWithEmail(
    email: string,
    verifyCode: string,
    password: string,
  ): IRequestPromise<void>;

  resetPasswordWithToken(
    pass: string,
    token: string,
    isPartnerAdminExpected?: boolean,
  ): Promise<void>;

  readPasswordRule(): IRequestPromise<SystemPasswordRule>;
}
