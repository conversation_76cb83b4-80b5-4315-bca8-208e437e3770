import { IMxUser, LoginOption, MxLoginType } from './mxUser';
import { User } from '@moxo/proto';
import { IRequestPromise, RequestPromise } from '../network/ajax';
import { sendBizServerRequest } from '../common/bizServerRequest';
import { requestNode } from '../common/requestNode';
import { ReadonlyDeep } from 'type-fest';
import { deepFreeze } from '@moxo/shared';

export class MxUserImpl implements IMxUser {
  private _user: User;
  get isLogin() {
    return !!this._user.id;
  }
  get id() {
    return this._user.id;
  }

  constructor(user: User) {
    this._user = user || {};
  }

  login(opt?: LoginOption): IRequestPromise<ReadonlyDeep<User>> {
    let isAutoLogin: boolean = !opt || !opt.pass;
    if (opt) {
      if (
        opt.loginType === MxLoginType.VERIFICATION_CODE ||
        opt.loginType === MxLoginType.ORG_INVITE_TOKEN ||
        opt.loginType === MxLoginType.APPLE_JWT ||
        opt.loginType === MxLoginType.GOOGLE_JWT ||
        opt.loginType === MxLoginType.SALESFORCE_JWT
      ) {
        isAutoLogin = false;
      }
    }
    if (isAutoLogin && this.isLogin) {
      // already login?
      return RequestPromise.createResolve(deepFreeze(this._user));
    }
    const body = requestNode();
    return sendBizServerRequest(body, {}, (response) => {
      const user = response?.object?.user || ({} as User);
      this._user = user;
      return user;
    });
  }
}
