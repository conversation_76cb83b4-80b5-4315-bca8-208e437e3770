import { ClientRequest, ClientResponse, ClientResponseCode } from '@moxo/proto';
import {
  AjaxContentType,
  AjaxMethod,
  IRequestParam,
  IRequestPromise,
  RequestPromise,
  sendRequest,
} from '../network/ajax';
import sdkConfig from './config';
import { BizServerError, convertToBizServerError } from './bizServerError';

export const SUCCESS_RESPONSE_CODES = [
  ClientResponseCode.RESPONSE_SUCCESS,
  ClientResponseCode.RESPONSE_ACCEPTED,
  ClientResponseCode.RESPONSE_NO_CONTENT,
  ClientResponseCode.RESPONSE_CONNECT_SUCCESS,
] as const;

/**
 * 成功响应状态码类型
 */
export type SuccessResponseCode = (typeof SUCCESS_RESPONSE_CODES)[number];

export function isSuccessResponse(response: ClientResponse): boolean {
  if (!response.code) {
    return true;
  }
  return SUCCESS_RESPONSE_CODES.includes(response.code as SuccessResponseCode);
}

export function sendBizServerRequest<T>(
  body: ClientRequest,
  opts: Partial<IRequestParam>,
  formater: (response: ClientResponse) => T,
): IRequestPromise<T> {
  let requestUrl = window.location.origin + sdkConfig.servicePath;
  if (body.object?.board) {
    requestUrl += '/board';
  } else if (body.object?.user) {
    requestUrl += '/user';
  } else if (body.object?.group) {
    requestUrl += '/group';
  }
  const opt = { method: AjaxMethod.POST, ...opts };

  if (!opt.contentType) {
    opt.contentType = AjaxContentType.JSON;
  }
  const requestPromise = sendRequest(requestUrl, opt, body);
  return new RequestPromise((resolve, reject) => {
    requestPromise
      .then((response) => {
        const clientResponse = response as ClientResponse;

        // 检查响应是否表示成功
        const isSuccess = isSuccessResponse(clientResponse);

        if (!isSuccess) {
          reject(convertToBizServerError(clientResponse));
        } else {
          if (formater) {
            resolve(formater(clientResponse));
          } else {
            resolve(clientResponse as T);
          }
        }
      })
      .catch((error) => {
        reject(convertToBizServerError(error));
      });
  }, requestPromise.requestId);
}
